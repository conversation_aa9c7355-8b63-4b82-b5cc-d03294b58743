#!/usr/bin/env python3
"""
Sprite Test Suite
Tests all sprite tools in the main gaia_sprites folder.
"""

import sys
import traceback
from pathlib import Path

def test_agf_agsearch():
    """Test the agf agsearch sprite"""
    print("Testing agf agsearch sprite...")
    try:
        from spr_agf_agsearch import quick_search
        
        # Test with a simple query
        results, stats = quick_search("AI agriculture")
        
        # Basic validation
        assert isinstance(results, list), "Results should be a list"
        assert isinstance(stats, dict), "Stats should be a dictionary"
        assert 'hits' in stats, "Stats should contain 'hits' key"
        
        print(f"✓ agf agsearch test passed - found {len(results)} results, {stats.get('hits', 0)} total hits")
        return True

    except Exception as e:
        print(f"✗ agf agsearch test failed: {e}")
        traceback.print_exc()
        return False

def test_tamarind():
    """Test the tamarind sprite"""
    print("Testing tamarind sprite...")
    try:
        from spr_tamarind import market_research

        # Test with a simple sector
        results = market_research("AI Agriculture")

        # Basic validation - results can be None if no data found
        if results is not None:
            assert isinstance(results, dict), "Results should be a dictionary when not None"
            print("✓ tamarind test passed - market research data retrieved")
        else:
            print("✓ tamarind test passed - no market data found (expected for some sectors)")

        return True

    except Exception as e:
        print(f"✗ tamarind test failed: {e}")
        traceback.print_exc()
        return False

def test_ext_search():
    """Test the external search sprite"""
    print("Testing external search sprite...")
    try:
        from spr_ext_search import web_search, news_search, business_news

        # Test basic web search
        results, stats = web_search("AI agriculture", num_results=3)

        # Basic validation
        assert isinstance(results, list), "Results should be a list"
        assert isinstance(stats, dict), "Stats should be a dictionary"
        assert 'total_results' in stats, "Stats should contain 'total_results' key"

        print(f"✓ external search test passed - found {stats.get('total_results', 0)} results")
        if results:
            print(f"Sample result: {results[0].get('title', 'N/A')}")

        # Test news search functionality
        try:
            news_results, news_stats = news_search("climate change", num_results=2)
            print(f"✓ news search test passed - found {news_stats.get('total_results', 0)} results")
        except Exception as e:
            print(f"✗ news search test failed: {e}")

        # Test business news functionality
        try:
            biz_results, biz_stats = business_news("fintech", num_results=2)
            print(f"✓ business news test passed - found {biz_stats.get('total_results', 0)} results")
        except Exception as e:
            print(f"✗ business news test failed: {e}")

        return True

    except Exception as e:
        print(f"✗ external search test failed: {e}")
        traceback.print_exc()
        return False

def test_agf_omnisearch():
    """Test the agf omnisearch sprite"""
    print("Testing agf omnisearch sprite...")
    try:
        from spr_agf_omnisearch import omni_search

        # Test with a simple query
        results, stats = omni_search("deepnote", limit=3)

        # Basic validation
        assert isinstance(results, list), "Results should be a list"
        assert isinstance(stats, dict), "Stats should be a dictionary"
        assert 'total_results' in stats, "Stats should contain 'total_results' key"

        print(f"✓ agf omnisearch test passed - found {stats.get('total_results', 0)} results")
        if results:
            print(f"Sample result: {results[0].get('name', 'N/A')}")

        return True

    except Exception as e:
        print(f"✗ agf omnisearch test failed: {e}")
        traceback.print_exc()
        return False



def test_ext_wiki():
    """Test the external wiki sprite"""
    print("Testing external wiki sprite...")
    try:
        from spr_ext_wiki import wiki_search

        # Test with a simple query
        results, stats = wiki_search("Apple Inc", sentences=2)

        # Basic validation
        assert isinstance(results, list), "Results should be a list"
        assert isinstance(stats, dict), "Stats should be a dictionary"
        assert 'total_results' in stats, "Stats should contain 'total_results' key"

        print(f"✓ external wiki test passed - found {stats.get('total_results', 0)} results")
        if results:
            print(f"Sample result: {results[0].get('title', 'N/A')}")

        return True

    except Exception as e:
        print(f"✗ external wiki test failed: {e}")
        traceback.print_exc()
        return False

def test_agf_frames():
    """Test the agf frames sprite"""
    print("Testing agf frames sprite...")
    try:
        from spr_agf_frames import frame_list, frame_search

        # Test frame list
        frames, stats = frame_list()

        # Basic validation
        assert isinstance(frames, list), "Frames should be a list"
        assert isinstance(stats, dict), "Stats should be a dictionary"
        assert 'total_frames' in stats, "Stats should contain 'total_frames' key"

        print(f"✓ agf frames test passed - found {stats.get('total_frames', 0)} frames")
        if frames:
            print(f"Sample frame: {frames[0].get('partial_slug', 'N/A')}")

        return True

    except Exception as e:
        print(f"✗ agf frames test failed: {e}")
        traceback.print_exc()
        return False

def test_agf_investors():
    """Test the agf investors sprite"""
    print("Testing agf investors sprite...")
    try:
        from spr_agf_investors import investor_search, investor_rankings

        # Test investor search (will likely fail due to missing data, but should handle gracefully)
        results, stats = investor_search("Sequoia", limit=5)

        # Basic validation
        assert isinstance(results, list), "Results should be a list"
        assert isinstance(stats, dict), "Stats should be a dictionary"

        if 'error' in stats:
            print(f"✓ agf investors test passed (expected error due to missing data): {stats['error']}")
        else:
            print(f"✓ agf investors test passed - found {stats.get('total_results', 0)} investors")

        return True

    except Exception as e:
        print(f"✗ agf investors test failed: {e}")
        traceback.print_exc()
        return False

def test_agf_multimodel():
    """Test the agf multimodel sprite"""
    print("Testing agf multimodel sprite...")
    try:
        from spr_agf_multimodel import multimodel_fast

        # Test with a simple query using fast models
        result = multimodel_fast("What is AI?", num_models=2)

        # Basic validation
        assert isinstance(result, dict), "Result should be a dictionary"
        assert 'results' in result, "Result should contain 'results' key"
        assert 'models' in result, "Result should contain 'models' key"

        if 'error' not in result:
            successful = len([r for r in result['results'].values() if r is not None])
            print(f"✓ agf multimodel test passed - got {successful}/{len(result['models'])} successful responses")
        else:
            print(f"✓ agf multimodel test passed (with expected error): {result['error']}")

        return True

    except Exception as e:
        print(f"✗ agf multimodel test failed: {e}")
        traceback.print_exc()
        return False

def test_agf_sql_agent():
    """Test the agf sql agent sprite"""
    print("Testing agf sql agent sprite...")
    try:
        from spr_agf_sql_agent import sql_query, list_databases

        # Test database listing
        dbs = list_databases()
        assert isinstance(dbs, dict), "Databases should be a dictionary"

        if 'error' not in dbs:
            print(f"✓ agf sql agent database listing passed - found {len(dbs)} databases")

            # Test simple query
            results, stats = sql_query("count organizations", execute=False)
            assert isinstance(stats, dict), "Stats should be a dictionary"

            if stats.get('success'):
                print(f"✓ agf sql agent query test passed - SQL generated successfully")
            else:
                print(f"✓ agf sql agent query test passed (with expected error): {stats.get('error')}")
        else:
            print(f"✓ agf sql agent test passed (with expected error): {dbs['error']}")

        return True

    except Exception as e:
        print(f"✗ agf sql agent test failed: {e}")
        traceback.print_exc()
        return False

def test_agf_agsearch_models():
    """Test the agf agsearch models sprite"""
    print("Testing agf agsearch models sprite...")
    try:
        from spr_agf_agsearch_models import list_contexts

        # Test context listing
        contexts = list_contexts(limit=3)
        assert isinstance(contexts, list), "Contexts should be a list"

        print(f"✓ agf agsearch models test passed - found {len(contexts)} contexts")
        return True

    except Exception as e:
        print(f"✗ agf agsearch models test failed: {e}")
        traceback.print_exc()
        return False

def test_simple_chart():
    """Test the simple chart sprite"""
    print("Testing simple chart sprite...")
    try:
        from spr_simple_chart import simple_chart_svg

        # Test chart generation
        data = "x,y\n1,10\n2,20\n3,15"
        result = simple_chart_svg(data, "line chart")

        assert isinstance(result, dict), "Result should be a dictionary"
        assert 'success' in result, "Result should contain 'success' key"

        if result.get('success'):
            print(f"✓ simple chart test passed - chart generated successfully")
        else:
            print(f"✓ simple chart test passed (with expected error): {result.get('error')}")

        return True

    except Exception as e:
        print(f"✗ simple chart test failed: {e}")
        traceback.print_exc()
        return False

def test_elf_mem():
    """Test the elf mem sprite"""
    print("Testing elf mem sprite...")
    try:
        from spr_elf_mem import elf_mem_store, elf_mem_get

        # Test memory operations
        result = elf_mem_store("test/bank", "test_key", {"test": "value"})
        assert isinstance(result, dict), "Result should be a dictionary"

        print(f"✓ elf mem test passed - memory operations available")
        return True

    except Exception as e:
        print(f"✗ elf mem test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all sprite tests"""
    print("=" * 50)
    print("SPRITE TEST SUITE")
    print("=" * 50)
    
    # Get all Python files in current directory (excluding this test file)
    current_dir = Path(__file__).parent
    sprite_files = [f for f in current_dir.glob("*.py") 
                   if f.name not in ["sprite_test.py", "__init__.py"]]
    
    print(f"Found sprite files: {[f.name for f in sprite_files]}")
    print()
    
    # Test each sprite
    test_results = {}
    
    # Test agf agsearch
    if Path(current_dir / "spr_agf_agsearch.py").exists():
        test_results['agf_agsearch'] = test_agf_agsearch()

    # Test tamarind
    if Path(current_dir / "spr_tamarind.py").exists():
        test_results['tamarind'] = test_tamarind()

    # Test external search
    if Path(current_dir / "spr_ext_search.py").exists():
        test_results['ext_search'] = test_ext_search()

    # Test agf omnisearch
    if Path(current_dir / "spr_agf_omnisearch.py").exists():
        test_results['agf_omnisearch'] = test_agf_omnisearch()

    # Test external wiki
    if Path(current_dir / "spr_ext_wiki.py").exists():
        test_results['ext_wiki'] = test_ext_wiki()

    # Test agf frames
    if Path(current_dir / "spr_agf_frames.py").exists():
        test_results['agf_frames'] = test_agf_frames()

    # Test agf investors
    if Path(current_dir / "spr_agf_investors.py").exists():
        test_results['agf_investors'] = test_agf_investors()

    # Test agf multimodel
    if Path(current_dir / "spr_agf_multimodel.py").exists():
        test_results['agf_multimodel'] = test_agf_multimodel()

    # Test agf sql agent
    if Path(current_dir / "spr_agf_sql_agent.py").exists():
        test_results['agf_sql_agent'] = test_agf_sql_agent()

    # Test agf agsearch models
    if Path(current_dir / "spr_agf_agsearch_models.py").exists():
        test_results['agf_agsearch_models'] = test_agf_agsearch_models()

    # Test simple chart
    if Path(current_dir / "spr_simple_chart.py").exists():
        test_results['simple_chart'] = test_simple_chart()

    # Test elf mem
    if Path(current_dir / "spr_elf_mem.py").exists():
        test_results['elf_mem'] = test_elf_mem()

    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(test_results.values())
    total = len(test_results)
    
    for sprite, result in test_results.items():
        status = "PASS" if result else "FAIL"
        print(f"{sprite}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All sprite tests passed!")
        return 0
    else:
        print("❌ Some sprite tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
