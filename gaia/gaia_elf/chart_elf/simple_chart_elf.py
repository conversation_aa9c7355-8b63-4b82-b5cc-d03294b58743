'''
# SIMPLE CHART ELF
# LLM context:
- sample bubble chart with style
- input: Pandas type table in text format

Process:
- LLM outputs pandas output_script like sample chart but with data, dimensions, etc from input table and spec
- execute output_script to produce SVG
- return SVG chart as string


# Test:
- input very simple country/gdp data text
- ask for bubble chart

'''

import os
import sys
import tempfile
import subprocess
import time
import shutil

# Add the project root to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from gaia.gaia_llm import gaia_llm


def simple_chart(input_data_table_str, input_chart_spec_str, output_format="svg"):
    """
    Generate a chart from input data with intelligent chart type selection.

    Args:
        input_data_table_str: String representation of data table
        input_chart_spec_str: Chart specification/requirements
        output_format: "svg" for SVG string, "png" for PNG file path

    Returns:
        dict: {
            "format": "svg" | "png_path",
            "content": "<svg>...</svg>" | "/full/path/to/chart.png",
            "chart_type": "bubble_chart" | "bar_chart" | "timeseries_chart"
        }
    """
    chart_type = llm__determine_chart_type(input_data_table_str, input_chart_spec_str)

    # Map chart types to sample paths
    sample_paths = {
        "bubble_chart": "./sample_chart_code__bubble.py.txt",
        "bar_chart": "./sample_chart_code__bar.txt",
        "timeseries_chart": "./sample_code_chart__timeseries.txt"
    }

    # Get sample path, fallback to bubble chart
    sample_chart_path = sample_paths.get(chart_type, sample_paths["bubble_chart"])
    if chart_type not in sample_paths:
        chart_type = "bubble_chart"  # Update chart_type for consistency

    # Generate chart with specified format
    result = _generate_chart(
        input_data_table_str,
        sample_chart_path,
        chart_type,
        output_format
    )

    return {
        "format": "svg" if output_format == "svg" else "png_path",
        "content": result,
        "chart_type": chart_type
    }



def llm__determine_chart_type( input_data_table_str, input_chart_spec_str,):
    options = ["bubble_chart", "bar_chart", "timeseries_chart"]

    # construct chart type selection prompt
    prompt = f"""
You are a data visualization expert. Analyze the provided data table and chart specification to determine the most appropriate chart type.

INPUT DATA TABLE:
{input_data_table_str}

CHART SPECIFICATION:
{input_chart_spec_str}

AVAILABLE CHART TYPES:
- bubble_chart: Best for showing relationships between 3+ variables (x, y, size, and optionally color/category)
- bar_chart: Best for comparing categorical data or discrete values
- timeseries_chart: Best for showing data changes over time with date/time columns

TASK:
1. Analyze the data table structure and identify the types of columns (numeric, categorical, temporal)
2. Consider the chart specification requirements
3. Choose the most appropriate chart type from the available options
4. Provide reasoning for your choice

Return your response as JSON with this structure:
{{
    "chart_type": "one of: bubble_chart, bar_chart, timeseries_chart",
    "reasoning": "explanation of why this chart type is most appropriate",
    "data_analysis": {{
        "numeric_columns": ["list of numeric column names"],
        "categorical_columns": ["list of categorical column names"],
        "temporal_columns": ["list of date/time column names"],
        "total_columns": "number of columns in dataset"
    }}
}}
"""

    # execute chart type selection prompt
    model = "anthropic/claude-sonnet-4-20250514"  # User's preferred model
    llm_client = gaia_llm.Json_LlmClient_Cached(model=model)

    try:
        response = llm_client.completion_json(
            user_prompt=prompt,
            verbose=False,
            log_context={"task": "chart_type_selection"}
        )

        if not response or "chart_type" not in response:
            # Fallback to bubble_chart if LLM response is invalid
            return "bubble_chart"

        chart_type = response["chart_type"]

        # Validate that the returned chart type is in our options
        if chart_type not in options:
            # Fallback to bubble_chart if invalid option returned
            return "bubble_chart"

        # return chart type
        return chart_type

    except Exception as e:
        # Fallback to bubble_chart on any error
        print(f"Error in chart type determination: {e}")
        return "bubble_chart"



def simple_bubble_chart__svg(input_data_table_str, sample_bubble_chart_path="./sample_chart_code__bubble.py.txt"):
    """
    Generate an SVG bubble chart from input data using LLM to adapt sample chart code.

    DEPRECATED: Use simple_chart() with output_format="svg" instead.

    Args:
        input_data_table_str: String representation of data table (CSV-like format)
        sample_bubble_chart_path: Path to sample chart code template

    Returns:
        str: SVG content as string
    """
    return _generate_chart(input_data_table_str, sample_bubble_chart_path, "bubble_chart", "svg")


def _generate_chart(input_data_table_str, sample_chart_path, chart_type, output_format="svg"):
    """
    Generate a chart in the specified format (SVG string or PNG file path).

    Args:
        input_data_table_str: String representation of data table
        sample_chart_path: Path to sample chart code template
        chart_type: Type of chart being generated
        output_format: "svg" for SVG string, "png" for PNG file path

    Returns:
        str: SVG content string or full path to PNG file
    """
    # Chart-specific instructions mapping
    instructions_map = {
        "bubble_chart": """3. Map the input data columns to appropriate chart dimensions (item name, x-axis, y-axis, bubble size, categories.  Ensure item name is visible on each bubble without hover.
""",
        "bar_chart": "3. Map the input data columns to appropriate chart dimensions (categories for x-axis, values for y-axis, optional grouping)",
        "timeseries_chart": "3. Map the input data columns to appropriate chart dimensions (time/date for x-axis, numeric values for y-axis, optional series grouping)"
    }


    chart_specific_instructions = instructions_map.get(chart_type, instructions_map["bubble_chart"])

    if output_format == "svg":
        return _generate_chart_svg(input_data_table_str, sample_chart_path, chart_type, chart_specific_instructions)
    elif output_format == "png":
        return _generate_chart_png(input_data_table_str, sample_chart_path, chart_type, chart_specific_instructions)
    else:
        raise ValueError(f"Unsupported output format: {output_format}")


def _generate_chart_svg(input_data_table_str, sample_chart_path, chart_type, chart_specific_instructions):
    """
    Generic chart generation function that reduces code duplication.

    Args:
        input_data_table_str: String representation of data table
        sample_chart_path: Path to sample chart code template
        chart_type: Type of chart being generated (for logging)
        chart_specific_instructions: Chart-specific instructions for the LLM

    Returns:
        str: SVG content as string
    """
    # Read the sample chart code
    try:
        with open(sample_chart_path, 'r') as f:
            sample_chart_code = f.read()
    except FileNotFoundError:
        # Try relative to this file's directory
        current_dir = os.path.dirname(__file__)
        full_path = os.path.join(current_dir, sample_chart_path)
        with open(full_path, 'r') as f:
            sample_chart_code = f.read()

    # Initialize LLM client
    model = "anthropic/claude-sonnet-4-20250514"  # User's preferred model
    llm_client = gaia_llm.Json_LlmClient_Cached(model=model)

    # Create prompt for LLM
    prompt = f"""
You are a data visualization expert. I need you to adapt the provided sample {chart_type} code to work with new input data.

INPUT DATA TABLE:
{input_data_table_str}

SAMPLE CHART CODE:
{sample_chart_code}

TASK:
1. Analyze the input data table format and identify the columns/fields available
2. Adapt the sample chart code to use the actual input data instead of the generated sample data
{chart_specific_instructions}
4. Ensure the chart title, axis labels, and hover text reflect the actual data being visualized
5. Keep all the sophisticated styling and layout parameters from the sample
6. Make sure the final code saves the chart as an SVG file

NOTE: Decide on Scaling:
- Determine scale of all axes.
- Look at spread of each of those
- If either axis has extreme scale differences, use a log scale for that axis as appropriate.

And in case of bubble charts:
- Determine bubble size for each row, if bubble size variation is extemely large, use a sqrt or log scale for the bubble size
- Final bubble size must be at most 5% of the chart width, but at least 0.2%



REQUIREMENTS:
- The output should be complete, executable Python code
- Use the same plotly styling and layout approach as the sample
- Adapt data loading to parse the input data table string
- Update titles, labels, and hover text to match the data
- Keep the SVG export functionality at the end

IMPORTANT PLOTLY CONSTRAINTS:
- NEVER use 'weight' property in textfont dictionaries (not supported by Plotly)
- For textfont 'style' property, only use 'normal' or 'italic' (NEVER 'bold')
- To make text bold, use HTML formatting in the text content itself, not font properties
- Only use valid Plotly properties as documented in the official Plotly documentation


Return your response as JSON with this structure:
{{
    "python_code": "complete executable Python code here",
    "data_mapping": {{
        "primary_column": "name of primary data column",
        "secondary_column": "name of secondary data column (if applicable)",
        "additional_info": "any other relevant mapping information"
    }},
    "chart_description": "brief description of what the chart shows"
}}
"""

    # Get LLM response
    try:
        response = llm_client.completion_json(
            user_prompt=prompt,
            verbose=False,
            log_context={"task": f"{chart_type}_generation"}
        )

        if not response or "python_code" not in response:
            raise ValueError("LLM did not return valid response with python_code")

        python_code = response["python_code"]

        # Execute the generated Python code to create SVG
        with tempfile.TemporaryDirectory() as temp_dir:
            # Write the Python code to a temporary file
            code_file = os.path.join(temp_dir, "chart_generator.py")
            with open(code_file, 'w') as f:
                f.write(python_code)

            # Execute the code
            result = subprocess.run(
                ["python", code_file],
                cwd=temp_dir,
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                raise RuntimeError(f"Chart generation failed: {result.stderr}")

            # Find the generated SVG file
            svg_files = [f for f in os.listdir(temp_dir) if f.endswith('.svg')]
            if not svg_files:
                raise RuntimeError("No SVG file was generated")

            # Read the SVG content
            svg_file = os.path.join(temp_dir, svg_files[0])
            with open(svg_file, 'r') as f:
                svg_content = f.read()

        return svg_content

    except Exception as e:
        # Return error information for debugging
        return f"<svg><text x='10' y='30'>Error generating {chart_type}: {str(e)}</text></svg>"


def _generate_chart_png(input_data_table_str, sample_chart_path, chart_type, chart_specific_instructions):
    """
    Generate a PNG chart file and return the full path to it.

    Args:
        input_data_table_str: String representation of data table
        sample_chart_path: Path to sample chart code template
        chart_type: Type of chart being generated
        chart_specific_instructions: Chart-specific instructions for the LLM

    Returns:
        str: Full path to the generated PNG file
    """
    # Read the sample chart code
    try:
        with open(sample_chart_path, 'r') as f:
            sample_chart_code = f.read()
    except FileNotFoundError:
        # Try relative to this file's directory
        current_dir = os.path.dirname(__file__)
        full_path = os.path.join(current_dir, sample_chart_path)
        with open(full_path, 'r') as f:
            sample_chart_code = f.read()

    # Initialize LLM client
    model = "anthropic/claude-sonnet-4-20250514"  # User's preferred model
    llm_client = gaia_llm.Json_LlmClient_Cached(model=model)

    # Create prompt for LLM - modified to generate PNG instead of SVG
    prompt = f"""
You are a data visualization expert. I need you to adapt the provided sample {chart_type} code to work with new input data and output a PNG file.

INPUT DATA TABLE:
{input_data_table_str}

SAMPLE CHART CODE:
{sample_chart_code}

TASK:
1. Analyze the input data table format and identify the columns/fields available
2. Adapt the sample chart code to use the actual input data instead of the generated sample data
{chart_specific_instructions}
4. Ensure the chart title, axis labels, and hover text reflect the actual data being visualized
5. Keep all the sophisticated styling and layout parameters from the sample
6. IMPORTANT: Make sure the final code saves the chart as a PNG file (not SVG)

REQUIREMENTS:
- The output should be complete, executable Python code
- Use the same plotly styling and layout approach as the sample
- Adapt data loading to parse the input data table string
- Update titles, labels, and hover text to match the data
- CRITICAL: Use fig.write_image("chart.png") to save as PNG instead of SVG
- Include any necessary imports for PNG export (like kaleido: pip install kaleido)

Return your response as JSON with this structure:
{{
    "python_code": "complete executable Python code here",
    "data_mapping": {{
        "primary_column": "name of primary data column",
        "secondary_column": "name of secondary data column (if applicable)",
        "additional_info": "any other relevant mapping information"
    }},
    "chart_description": "brief description of what the chart shows"
}}
"""
    # Get LLM response
    try:
        response = llm_client.completion_json(
            user_prompt=prompt,
            verbose=False,
            log_context={"task": f"{chart_type}_png_generation"}
        )

        if not response or "python_code" not in response:
            raise ValueError("LLM did not return valid response with python_code")

        python_code = response["python_code"]

        # Create a persistent directory for PNG files (not temporary)
        output_dir = os.path.join(tempfile.gettempdir(), "chart_outputs")
        os.makedirs(output_dir, exist_ok=True)

        # Generate unique filename
        timestamp = int(time.time() * 1000)  # milliseconds for uniqueness
        png_filename = f"{chart_type}_{timestamp}.png"
        expected_png_path = os.path.join(output_dir, png_filename)

        # Execute the generated Python code to create PNG
        with tempfile.TemporaryDirectory() as temp_dir:
            # Write the Python code to a temporary file
            code_file = os.path.join(temp_dir, "chart_generator.py")
            with open(code_file, 'w') as f:
                f.write(python_code)

            # Execute the code
            result = subprocess.run(
                ["python", code_file],
                cwd=temp_dir,
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                raise RuntimeError(f"Chart generation failed: {result.stderr}")

            # Find the generated PNG file in temp directory
            png_files = [f for f in os.listdir(temp_dir) if f.endswith('.png')]
            if not png_files:
                raise RuntimeError("No PNG file was generated")

            # Move the PNG file to persistent location
            temp_png_path = os.path.join(temp_dir, png_files[0])
            shutil.move(temp_png_path, expected_png_path)

        return expected_png_path

    except Exception as e:
        # Create an error PNG file and return its path
        error_png_path = os.path.join(tempfile.gettempdir(), f"error_{chart_type}_{int(time.time())}.png")

        # Create a simple error image using PIL if available, otherwise return error path
        try:
            from PIL import Image, ImageDraw, ImageFont
            img = Image.new('RGB', (400, 200), color='white')
            draw = ImageDraw.Draw(img)
            try:
                font = ImageFont.load_default()
            except:
                font = None
            draw.text((10, 50), f"Error generating {chart_type}:", fill='red', font=font)
            draw.text((10, 80), str(e)[:50] + "..." if len(str(e)) > 50 else str(e), fill='red', font=font)
            img.save(error_png_path)
            return error_png_path
        except ImportError:
            # If PIL not available, just return a path indicating error
            return f"ERROR_PATH: Error generating {chart_type}: {str(e)}"


# Note: Removed deprecated functions that had no external usage:
# - simple_bar_chart__svg (deprecated, no external references)
# - simple_timeseries_chart__svg (deprecated, no external references)
# - simple_bubble_chart__png (no external references)
# - simple_bar_chart__png (no external references)
# - simple_timeseries_chart__png (no external references)
#
# The main simple_chart() function provides all this functionality with better API design.
# For PNG generation: simple_chart(data, spec, output_format="png")
# For specific chart types: simple_chart(data, "create a bar chart", output_format="svg")

